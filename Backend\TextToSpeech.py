import pygame
import random
import asyncio
import edge_tts
import os
from dotenv import dotenv_values

# Load environment variables
env_vars = dotenv_values(".env")

# AI Voice Configuration - Heavy, Dangerous, Savage AI Persona
AI_VOICE_OPTIONS = [
    "en-US-SteffanNeural",      # Deep, authoritative male voice
    "en-US-DavisNeural",        # Strong, commanding male voice
    "en-US-JasonNeural",        # Powerful, intense male voice
    "en-GB-RyanNeural",         # British, sophisticated but menacing
    "en-AU-WilliamNeural",      # Australian, rugged and tough
]

# Select primary AI voice (can be overridden by .env)
AssistantVoice = env_vars.get("AssistantVoice", "en-US-SteffanNeural")

# Advanced AI Voice Parameters for Heavy, Dangerous Sound
AI_VOICE_SETTINGS = {
    "pitch": "-15Hz",
    "rate": "+15%",            # Faster speech for better user experience
    "volume": "+10%",
}

# Path for audio file
AUDIO_FILE_PATH = r"Data\speech.mp3"

def enhance_ai_speech(text: str) -> str:
    """Enhance text for more AI-like, heavy, dangerous delivery."""
    # Remove symbols that shouldn't be spoken
    symbols_to_remove = ['*', ':', ';', '~', '#', '!']
    enhanced = text
    for symbol in symbols_to_remove:
        enhanced = enhanced.replace(symbol, '')

    # Add strategic pauses for more menacing effect
    enhanced = enhanced.replace(". ", "... ")  # Longer pauses between sentences
    enhanced = enhanced.replace(", ", ".. ")  # Pauses after commas for emphasis

    # Add emphasis to certain AI-related words
    ai_emphasis_words = {
        "Matrix AI": "Matrix... A.I.",
        "artificial intelligence": "artificial... intelligence",
        "system": "system...",
        "processing": "processing...",
        "analyzing": "analyzing...",
        "calculating": "calculating...",
        "executing": "executing...",
        "initializing": "initializing...",
        "scanning": "scanning...",
        "detecting": "detecting...",
        "monitoring": "monitoring...",
        "accessing": "accessing...",
        "sir": "sir...",
        "human": "human...",
        "user": "user..."
    }

    # Apply emphasis to specific words
    for word, emphasized in ai_emphasis_words.items():
        enhanced = enhanced.replace(word, emphasized)
        enhanced = enhanced.replace(word.capitalize(), emphasized.capitalize())
        enhanced = enhanced.replace(word.upper(), emphasized.upper())

    # Add subtle threatening undertones to certain phrases
    threatening_replacements = {
        "I can help": "I... can assist",
        "How can I help": "How may I... serve",
        "I understand": "I... comprehend",
        "I will": "I shall...",
        "Let me": "Allow me to...",
        "I think": "I calculate...",
        "I believe": "I determine...",
        "I know": "I am... aware",
        "I see": "I observe...",
        "I found": "I have... located",
        "I'm sorry": "I... acknowledge",
        "Thank you": "Acknowledged...",
        "You're welcome": "It is... my function",
        "Hello": "Greetings...",
        "Hi": "Greetings...",
        "Good": "Acceptable...",
        "Great": "Satisfactory...",
        "Excellent": "Optimal...",
        "Perfect": "Precise..."
    }

    # Apply threatening replacements
    for original, replacement in threatening_replacements.items():
        enhanced = enhanced.replace(original, replacement)
        enhanced = enhanced.replace(original.lower(), replacement.lower())
        enhanced = enhanced.replace(original.upper(), replacement.upper())

    return enhanced

async def text_to_audio_file(text: str) -> None:
    """Generates an audio file from text using edge_tts with AI-like voice parameters."""
    if os.path.exists(AUDIO_FILE_PATH):
        os.remove(AUDIO_FILE_PATH)

    enhanced_text = enhance_ai_speech(text)

    try:
        communicate = edge_tts.Communicate(
            enhanced_text,
            AssistantVoice,
            pitch=AI_VOICE_SETTINGS["pitch"],
            rate=AI_VOICE_SETTINGS["rate"],
            volume=AI_VOICE_SETTINGS["volume"]
        )
        await communicate.save(AUDIO_FILE_PATH)

    except Exception:
        # Try alternative AI voices if primary fails
        for fallback_voice in AI_VOICE_OPTIONS:
            try:
                communicate = edge_tts.Communicate(
                    enhanced_text,
                    fallback_voice,
                    pitch=AI_VOICE_SETTINGS["pitch"],
                    rate=AI_VOICE_SETTINGS["rate"],
                    volume=AI_VOICE_SETTINGS["volume"]
                )
                await communicate.save(AUDIO_FILE_PATH)
                break
            except Exception:
                continue

async def run_tts(text: str):
    """Runs text-to-speech conversion asynchronously."""
    await text_to_audio_file(text)

def apply_ai_audio_effects():
    """Apply additional audio effects to make voice more AI-like and menacing."""
    try:
        pygame.mixer.pre_init(frequency=22050, size=-16, channels=2, buffer=512)
        pygame.mixer.init()
        pygame.mixer.music.load(AUDIO_FILE_PATH)
        pygame.mixer.music.set_volume(0.9)
        return True
    except Exception:
        pygame.mixer.init()
        pygame.mixer.music.load(AUDIO_FILE_PATH)
        return False

def tts(text: str, func=lambda r=None: True):
    """Handles text-to-speech and plays the generated audio file with AI enhancements."""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    loop.run_until_complete(run_tts(text))

    apply_ai_audio_effects()
    pygame.mixer.music.play()

    clock = pygame.time.Clock()
    while pygame.mixer.music.get_busy():
        if not func():
            break
        clock.tick(10)

    pygame.mixer.music.stop()
    pygame.mixer.quit()

def text_to_speech(text: str, func=lambda r=None: True):
    """Splits text if too long and provides responses accordingly with AI-like menacing tone."""
    # AI-like, heavy, dangerous responses for long text
    ai_responses = [
        "Additional data has been... transmitted to your display terminal... sir...",
        "The remaining information is now... accessible on your screen... human...",
        "Further details have been... uploaded to your interface... sir...",
        "The complete data set... awaits your review on the display... user...",
        "Additional intelligence has been... processed to your terminal... sir...",
        "The extended analysis is now... available on your screen... human...",
        "Further computational results... have been delivered to your display... sir...",
        "The comprehensive data... has been transferred to your interface... user...",
        "Additional processing output... is now visible on your terminal... sir...",
        "The complete intelligence report... awaits on your display screen... human...",
        "Extended data analysis... has been uploaded to your interface... sir...",
        "The full computational result... is now accessible on your screen... user...",
        "Additional system output... has been transmitted to your terminal... sir...",
        "The complete information matrix... is displayed on your screen... human...",
        "Further data processing... has been delivered to your interface... sir...",
        "The extended intelligence... is now available on your display... user...",
        "Additional computational data... awaits your examination on screen... sir...",
        "The comprehensive analysis... has been transferred to your terminal... human...",
        "Further system intelligence... is now accessible on your display... sir...",
        "The complete data transmission... has been uploaded to your screen... user..."
    ]

    if len(text.split(".")) > 4 and len(text) > 250:
        # For long text, speak first part and add AI-like continuation message
        first_part = " ".join(text.split(".")[:2]) + "... "
        ai_continuation = random.choice(ai_responses)
        tts(first_part + ai_continuation, func)
    else:
        # For normal length text, speak with AI enhancements
        tts(text, func)

if __name__ == "__main__":
    while True:
        text_to_speech(input("Enter the Text: "))