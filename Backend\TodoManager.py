import json
import os
import datetime
from typing import List, Dict, Optional, Union
from dateutil import parser
from dateutil.relativedelta import relativedelta
import threading
import time
from Backend.TextToSpeech import text_to_speech
from Frontend.GUI import ShowTextToScreen, SetAssistantStatus
from dotenv import dotenv_values

# Load environment variables
env_vars = dotenv_values(".env")
Assistantname = env_vars.get("Assistantname", "Matrix")

class TodoManager:
    def __init__(self):
        self.todo_file = "Data/TodoList.json"
        self.tasks = []
        self.reminder_thread = None
        self.reminder_running = False
        self.load_tasks()
        self.start_reminder_system()

    def load_tasks(self):
        """Load tasks from JSON file"""
        try:
            if os.path.exists(self.todo_file):
                with open(self.todo_file, 'r', encoding='utf-8') as f:
                    self.tasks = json.load(f)
            else:
                self.tasks = []
                self.save_tasks()
        except Exception as e:
            print(f"Error loading tasks: {e}")
            self.tasks = []

    def save_tasks(self):
        """Save tasks to JSON file"""
        try:
            os.makedirs(os.path.dirname(self.todo_file), exist_ok=True)
            with open(self.todo_file, 'w', encoding='utf-8') as f:
                json.dump(self.tasks, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving tasks: {e}")

    def parse_datetime(self, datetime_str: str) -> Optional[datetime.datetime]:
        """Parse various datetime formats"""
        if not datetime_str or datetime_str.lower() in ['none', 'no', 'never']:
            return None

        try:
            # Handle relative dates
            now = datetime.datetime.now()
            datetime_str = datetime_str.lower().strip()

            # Common relative terms
            if datetime_str in ['today']:
                return now.replace(hour=18, minute=0, second=0, microsecond=0)
            elif datetime_str in ['tomorrow']:
                return (now + relativedelta(days=1)).replace(hour=18, minute=0, second=0, microsecond=0)
            elif datetime_str in ['next week']:
                return (now + relativedelta(weeks=1)).replace(hour=18, minute=0, second=0, microsecond=0)
            elif datetime_str in ['next month']:
                return (now + relativedelta(months=1)).replace(hour=18, minute=0, second=0, microsecond=0)
            elif 'monday' in datetime_str:
                days_ahead = 0 - now.weekday()
                if days_ahead <= 0:
                    days_ahead += 7
                return (now + relativedelta(days=days_ahead)).replace(hour=18, minute=0, second=0, microsecond=0)
            elif 'tuesday' in datetime_str:
                days_ahead = 1 - now.weekday()
                if days_ahead <= 0:
                    days_ahead += 7
                return (now + relativedelta(days=days_ahead)).replace(hour=18, minute=0, second=0, microsecond=0)
            elif 'wednesday' in datetime_str:
                days_ahead = 2 - now.weekday()
                if days_ahead <= 0:
                    days_ahead += 7
                return (now + relativedelta(days=days_ahead)).replace(hour=18, minute=0, second=0, microsecond=0)
            elif 'thursday' in datetime_str:
                days_ahead = 3 - now.weekday()
                if days_ahead <= 0:
                    days_ahead += 7
                return (now + relativedelta(days=days_ahead)).replace(hour=18, minute=0, second=0, microsecond=0)
            elif 'friday' in datetime_str:
                days_ahead = 4 - now.weekday()
                if days_ahead <= 0:
                    days_ahead += 7
                return (now + relativedelta(days=days_ahead)).replace(hour=18, minute=0, second=0, microsecond=0)
            elif 'saturday' in datetime_str:
                days_ahead = 5 - now.weekday()
                if days_ahead <= 0:
                    days_ahead += 7
                return (now + relativedelta(days=days_ahead)).replace(hour=18, minute=0, second=0, microsecond=0)
            elif 'sunday' in datetime_str:
                days_ahead = 6 - now.weekday()
                if days_ahead <= 0:
                    days_ahead += 7
                return (now + relativedelta(days=days_ahead)).replace(hour=18, minute=0, second=0, microsecond=0)

            # Try to parse with dateutil
            return parser.parse(datetime_str, fuzzy=True)
        except:
            return None

    def add_task(self, description: str, deadline: str = None, remind_time: str = None, priority: str = "medium") -> str:
        """Add a new task"""
        try:
            task_id = len(self.tasks) + 1

            # Parse deadline and reminder times
            deadline_dt = self.parse_datetime(deadline) if deadline else None
            remind_dt = self.parse_datetime(remind_time) if remind_time else None

            # If no reminder time specified but deadline exists, set reminder 1 hour before deadline
            if deadline_dt and not remind_dt:
                remind_dt = deadline_dt - relativedelta(hours=1)

            task = {
                "id": task_id,
                "description": description,
                "deadline": deadline_dt.isoformat() if deadline_dt else None,
                "remind_time": remind_dt.isoformat() if remind_dt else None,
                "priority": priority.lower(),
                "completed": False,
                "created_at": datetime.datetime.now().isoformat(),
                "completed_at": None
            }

            self.tasks.append(task)
            self.save_tasks()

            response = f"Task added successfully: '{description}'"
            if deadline_dt:
                response += f" with deadline on {deadline_dt.strftime('%A, %B %d at %I:%M %p')}"
            if remind_dt:
                response += f" and reminder set for {remind_dt.strftime('%A, %B %d at %I:%M %p')}"

            return response

        except Exception as e:
            return f"Error adding task: {str(e)}"

    def list_tasks(self, show_completed: bool = False) -> str:
        """List all tasks"""
        try:
            if not self.tasks:
                return "You have no tasks in your TO-DO list."

            active_tasks = [task for task in self.tasks if not task["completed"]]
            completed_tasks = [task for task in self.tasks if task["completed"]]

            if not active_tasks and not show_completed:
                return "You have no pending tasks. Great job!"

            response = ""

            if active_tasks:
                response += "Your pending tasks:\n"
                for i, task in enumerate(active_tasks, 1):
                    priority_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(task["priority"], "⚪")
                    response += f"{i}. {priority_emoji} {task['description']}"

                    if task["deadline"]:
                        deadline_dt = datetime.datetime.fromisoformat(task["deadline"])
                        response += f" (Due: {deadline_dt.strftime('%b %d, %I:%M %p')})"

                    response += "\n"

            if show_completed and completed_tasks:
                response += f"\nCompleted tasks ({len(completed_tasks)}):\n"
                for task in completed_tasks[-5:]:  # Show last 5 completed tasks
                    response += f"✅ {task['description']}\n"

            return response.strip()

        except Exception as e:
            return f"Error listing tasks: {str(e)}"

    def remove_task(self, task_identifier: Union[int, str]) -> str:
        """Remove a task by ID or description"""
        try:
            task_to_remove = None

            # Try to find by ID first (using display order of active tasks)
            if isinstance(task_identifier, int) or task_identifier.isdigit():
                task_id = int(task_identifier)
                active_tasks = [task for task in self.tasks if not task["completed"]]
                if 1 <= task_id <= len(active_tasks):
                    task_to_remove = active_tasks[task_id - 1]

            # If not found by ID, try to find by description
            if not task_to_remove:
                for task in self.tasks:
                    if not task["completed"] and task_identifier.lower() in task["description"].lower():
                        task_to_remove = task
                        break

            if task_to_remove:
                self.tasks.remove(task_to_remove)
                self.save_tasks()
                return f"Task removed: '{task_to_remove['description']}'"
            else:
                return f"Task not found: '{task_identifier}'"

        except Exception as e:
            return f"Error removing task: {str(e)}"

    def complete_task(self, task_identifier: Union[int, str]) -> str:
        """Mark a task as completed"""
        try:
            task_to_complete = None

            # Try to find by ID first (using display order of active tasks)
            if isinstance(task_identifier, int) or task_identifier.isdigit():
                task_id = int(task_identifier)
                active_tasks = [task for task in self.tasks if not task["completed"]]
                if 1 <= task_id <= len(active_tasks):
                    task_to_complete = active_tasks[task_id - 1]

            # If not found by ID, try to find by description
            if not task_to_complete:
                for task in self.tasks:
                    if not task["completed"] and task_identifier.lower() in task["description"].lower():
                        task_to_complete = task
                        break

            if task_to_complete:
                task_to_complete["completed"] = True
                task_to_complete["completed_at"] = datetime.datetime.now().isoformat()
                self.save_tasks()
                return f"Task completed: '{task_to_complete['description']}'. Well done!"
            else:
                return f"Task not found: '{task_identifier}'"

        except Exception as e:
            return f"Error completing task: {str(e)}"

    def edit_task(self, task_identifier: Union[int, str], new_details: str) -> str:
        """Edit a task"""
        try:
            task_to_edit = None

            # Try to find by ID first (using display order of active tasks)
            if isinstance(task_identifier, int) or task_identifier.isdigit():
                task_id = int(task_identifier)
                active_tasks = [task for task in self.tasks if not task["completed"]]
                if 1 <= task_id <= len(active_tasks):
                    task_to_edit = active_tasks[task_id - 1]

            # If not found by ID, try to find by description
            if not task_to_edit:
                for task in self.tasks:
                    if not task["completed"] and task_identifier.lower() in task["description"].lower():
                        task_to_edit = task
                        break

            if task_to_edit:
                old_description = task_to_edit["description"]

                # Parse new details for different types of edits
                if "deadline" in new_details.lower():
                    # Extract new deadline - handle "change deadline to X" format
                    if "change deadline to" in new_details.lower():
                        deadline_part = new_details.lower().split("change deadline to")[1].strip()
                    else:
                        deadline_part = new_details.lower().split("deadline")[1].strip()

                    new_deadline = self.parse_datetime(deadline_part)
                    if new_deadline:
                        task_to_edit["deadline"] = new_deadline.isoformat()
                        self.save_tasks()
                        return f"Updated deadline for '{old_description}' to {new_deadline.strftime('%A, %B %d at %I:%M %p')}"
                    else:
                        return f"Could not parse the new deadline: '{deadline_part}'. Please try a format like 'tomorrow', 'friday', or 'january 25 at 3pm'"

                elif "description" in new_details.lower() or "change to" in new_details.lower():
                    # Update description
                    if "change to" in new_details.lower():
                        new_desc = new_details.lower().split("change to")[1].strip()
                    else:
                        new_desc = new_details.replace("description", "").strip()

                    task_to_edit["description"] = new_desc
                    self.save_tasks()
                    return f"Updated task description from '{old_description}' to '{new_desc}'"

                else:
                    # Default: update description
                    task_to_edit["description"] = new_details
                    self.save_tasks()
                    return f"Updated task from '{old_description}' to '{new_details}'"
            else:
                return f"Task not found: '{task_identifier}'"

        except Exception as e:
            return f"Error editing task: {str(e)}"

    def get_task_summary(self) -> str:
        """Get a summary of tasks"""
        try:
            if not self.tasks:
                return "You have no tasks in your TO-DO list."

            active_tasks = [task for task in self.tasks if not task["completed"]]
            completed_tasks = [task for task in self.tasks if task["completed"]]

            # Count by priority
            high_priority = len([t for t in active_tasks if t["priority"] == "high"])
            medium_priority = len([t for t in active_tasks if t["priority"] == "medium"])
            low_priority = len([t for t in active_tasks if t["priority"] == "low"])

            # Count overdue tasks
            now = datetime.datetime.now()
            overdue_tasks = []
            for task in active_tasks:
                if task["deadline"]:
                    deadline_dt = datetime.datetime.fromisoformat(task["deadline"])
                    if deadline_dt < now:
                        overdue_tasks.append(task)

            response = f"Task Summary:\n"
            response += f"📋 Total tasks: {len(self.tasks)}\n"
            response += f"⏳ Pending: {len(active_tasks)}\n"
            response += f"✅ Completed: {len(completed_tasks)}\n"

            if active_tasks:
                response += f"\nPriority breakdown:\n"
                if high_priority > 0:
                    response += f"🔴 High priority: {high_priority}\n"
                if medium_priority > 0:
                    response += f"🟡 Medium priority: {medium_priority}\n"
                if low_priority > 0:
                    response += f"🟢 Low priority: {low_priority}\n"

            if overdue_tasks:
                response += f"\n⚠️ Overdue tasks: {len(overdue_tasks)}\n"
                for task in overdue_tasks[:3]:  # Show first 3 overdue tasks
                    deadline_dt = datetime.datetime.fromisoformat(task["deadline"])
                    days_overdue = (now - deadline_dt).days
                    response += f"  • {task['description']} (overdue by {days_overdue} days)\n"

            return response.strip()

        except Exception as e:
            return f"Error getting task summary: {str(e)}"

    def start_reminder_system(self):
        """Start the background reminder system"""
        if not self.reminder_running:
            self.reminder_running = True
            self.reminder_thread = threading.Thread(target=self._reminder_loop, daemon=True)
            self.reminder_thread.start()

    def stop_reminder_system(self):
        """Stop the background reminder system"""
        self.reminder_running = False
        if self.reminder_thread:
            self.reminder_thread.join(timeout=1)

    def _reminder_loop(self):
        """Background loop to check for reminders"""
        while self.reminder_running:
            try:
                now = datetime.datetime.now()

                for task in self.tasks:
                    if (not task["completed"] and
                        task["remind_time"] and
                        not task.get("reminder_sent", False)):

                        remind_dt = datetime.datetime.fromisoformat(task["remind_time"])

                        # Check if it's time for reminder (within 1 minute)
                        time_diff = (remind_dt - now).total_seconds()
                        if -60 <= time_diff <= 60:  # Within 1 minute window
                            self._send_reminder(task)
                            task["reminder_sent"] = True
                            self.save_tasks()

                # Check every 30 seconds
                time.sleep(30)

            except Exception as e:
                print(f"Error in reminder loop: {e}")
                time.sleep(60)  # Wait longer on error

    def _send_reminder(self, task):
        """Send a reminder for a task"""
        try:
            description = task["description"]
            reminder_message = f"Reminder: {description}"

            if task["deadline"]:
                deadline_dt = datetime.datetime.fromisoformat(task["deadline"])
                reminder_message += f" (Due: {deadline_dt.strftime('%A, %B %d at %I:%M %p')})"

            # Display on screen
            ShowTextToScreen(f"{Assistantname} : {reminder_message}")
            SetAssistantStatus("Reminder")

            # Speak the reminder
            text_to_speech(reminder_message)

            print(f"Reminder sent: {reminder_message}")

        except Exception as e:
            print(f"Error sending reminder: {e}")

    def get_upcoming_reminders(self, hours: int = 24) -> str:
        """Get reminders for the next specified hours"""
        try:
            now = datetime.datetime.now()
            future_time = now + relativedelta(hours=hours)

            upcoming = []
            for task in self.tasks:
                if (not task["completed"] and
                    task["remind_time"] and
                    not task.get("reminder_sent", False)):

                    remind_dt = datetime.datetime.fromisoformat(task["remind_time"])
                    if now <= remind_dt <= future_time:
                        upcoming.append((task, remind_dt))

            if not upcoming:
                return f"No reminders scheduled for the next {hours} hours."

            # Sort by reminder time
            upcoming.sort(key=lambda x: x[1])

            response = f"Upcoming reminders (next {hours} hours):\n"
            for task, remind_dt in upcoming:
                time_until = remind_dt - now
                hours_until = int(time_until.total_seconds() // 3600)
                minutes_until = int((time_until.total_seconds() % 3600) // 60)

                response += f"• {task['description']} "
                if hours_until > 0:
                    response += f"(in {hours_until}h {minutes_until}m)\n"
                else:
                    response += f"(in {minutes_until} minutes)\n"

            return response.strip()

        except Exception as e:
            return f"Error getting upcoming reminders: {str(e)}"


# Global instance
todo_manager = TodoManager()


# Voice command processing functions
def process_todo_command(command: str) -> str:
    """Process TO-DO voice commands"""
    try:
        command = command.strip().lower()

        if command.startswith("todo add"):
            # Parse: "todo add buy groceries deadline friday remind thursday"
            parts = command.replace("todo add ", "").split(" deadline ")
            description = parts[0].strip()

            deadline = None
            remind_time = None

            if len(parts) > 1:
                deadline_part = parts[1]
                if " remind " in deadline_part:
                    deadline_remind = deadline_part.split(" remind ")
                    deadline = deadline_remind[0].strip()
                    remind_time = deadline_remind[1].strip()
                else:
                    deadline = deadline_part.strip()

            return todo_manager.add_task(description, deadline, remind_time)

        elif command.startswith("todo list"):
            return todo_manager.list_tasks()

        elif command.startswith("todo remove"):
            task_id = command.replace("todo remove ", "").strip()
            return todo_manager.remove_task(task_id)

        elif command.startswith("todo complete"):
            task_id = command.replace("todo complete ", "").strip()
            return todo_manager.complete_task(task_id)

        elif command.startswith("todo edit"):
            # Parse: "todo edit 1 change deadline to friday"
            edit_part = command.replace("todo edit ", "")
            parts = edit_part.split(" ", 1)
            if len(parts) >= 2:
                task_id = parts[0]
                new_details = parts[1]
                return todo_manager.edit_task(task_id, new_details)
            else:
                return "Please specify what to edit. For example: 'edit task 1 change deadline to friday'"

        elif command.startswith("todo show"):
            return todo_manager.get_task_summary()

        else:
            return "Unknown TO-DO command. Try: add, list, remove, complete, edit, or show."

    except Exception as e:
        return f"Error processing TO-DO command: {str(e)}"
