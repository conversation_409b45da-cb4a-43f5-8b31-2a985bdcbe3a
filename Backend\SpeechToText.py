from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import WebDriverException, TimeoutException, NoSuchElementException
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from dotenv import dotenv_values
import os
import mtranslate as mt
import time
import threading
import atexit

# Load environment variables
env_vars = dotenv_values(".env")
InputLanguage = env_vars.get("InputLanguage")

# HTML Code for Speech Recognition with improved error handling
HtmlCode = '''<!DOCTYPE html>
<html lang="en">
<head>
    <title>Speech Recognition</title>
    <style>
        body { font-family: Arial, sans-serif; }
        #status { color: green; margin-top: 10px; }
        #error { color: red; margin-top: 10px; }
    </style>
</head>
<body>
    <button id="start" onclick="startRecognition()">Start Recognition</button>
    <button id="end" onclick="stopRecognition()">Stop Recognition</button>
    <p id="output"></p>
    <p id="status"></p>
    <p id="error"></p>
    <script>
        const output = document.getElementById('output');
        const status = document.getElementById('status');
        const errorDisplay = document.getElementById('error');
        let recognition;
        let recognitionActive = false;

        function startRecognition() {
            try {
                if (recognitionActive) {
                    stopRecognition();
                }

                recognition = new (window.SpeechRecognition || window.webkitSpeechRecognition)();
                recognition.lang = '';
                recognition.continuous = false;  // Changed to false for better stability
                recognition.interimResults = false;

                recognition.onstart = function() {
                    recognitionActive = true;
                    status.textContent = "Listening...";
                    errorDisplay.textContent = "";
                };

                recognition.onresult = function(event) {
                    const transcript = event.results[event.results.length - 1][0].transcript;
                    output.textContent += transcript;
                    status.textContent = "Speech recognized!";
                };

                recognition.onerror = function(event) {
                    errorDisplay.textContent = "Error: " + event.error;
                    if (event.error === 'no-speech') {
                        // Restart on no-speech error
                        setTimeout(() => {
                            if (recognitionActive) {
                                recognition.stop();
                                recognition.start();
                            }
                        }, 300);
                    }
                };

                recognition.onend = function() {
                    if (recognitionActive && output.textContent.trim() === "") {
                        // If nothing was recognized, restart
                        setTimeout(() => {
                            if (recognitionActive) {
                                recognition.start();
                            }
                        }, 300);
                    } else {
                        status.textContent = "Recognition ended";
                    }
                };

                recognition.start();
            } catch (e) {
                errorDisplay.textContent = "Failed to start recognition: " + e.message;
            }
        }

        function stopRecognition() {
            if (recognition) {
                recognitionActive = false;
                try {
                    recognition.stop();
                } catch (e) {
                    errorDisplay.textContent = "Error stopping recognition: " + e.message;
                }
            }
            status.textContent = "Recognition stopped";
        }
    </script>
</body>
</html>'''

HtmlCode = HtmlCode.replace("recognition.lang = '';", f"recognition.lang = '{InputLanguage}';")

# Save the HTML file
os.makedirs("Data", exist_ok=True)
with open(r"Data\Voice.html", "w") as f:
    f.write(HtmlCode)

current_dir = os.getcwd()
Link = f"{current_dir}/Data/Voice.html"

# Define Paths
TempDirPath = os.path.join(current_dir, "Frontend", "Files")
os.makedirs(TempDirPath, exist_ok=True)

# Global variables for WebDriver management
driver = None
driver_lock = threading.Lock()
driver_last_used = 0
driver_max_idle_time = 300  # 5 minutes
driver_restart_count = 0
MAX_RESTART_ATTEMPTS = 3

def initialize_webdriver():
    """Initialize a new WebDriver instance with appropriate options."""
    global driver, driver_last_used, driver_restart_count

    # Set Chrome Options
    chrome_options = Options()
    chrome_options.add_argument("--headless=new")  # Run Chrome in headless mode
    chrome_options.add_argument("--use-fake-ui-for-media-stream")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-logging")
    chrome_options.add_argument("--log-level=3")  # Only show fatal errors

    # Additional options for stability
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--allow-running-insecure-content")
    chrome_options.add_argument("--disable-features=IsolateOrigins,site-per-process")

    # Set User Agent
    user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/89.0.142.86 Safari/538.36"
    chrome_options.add_argument(f'user-agent={user_agent}')

    try:
        print(f"Initializing Chrome WebDriver (attempt {driver_restart_count + 1})...")
        service = Service(ChromeDriverManager().install())
        new_driver = webdriver.Chrome(service=service, options=chrome_options)
        print("Chrome WebDriver initialized successfully!")
        driver_last_used = time.time()
        driver_restart_count = 0
        return new_driver
    except Exception as e:
        print(f"Error initializing WebDriver: {e}")
        driver_restart_count += 1
        if driver_restart_count >= MAX_RESTART_ATTEMPTS:
            print("Maximum WebDriver restart attempts reached. Exiting.")
            return None
        time.sleep(2)  # Wait before retrying
        return initialize_webdriver()  # Recursive retry

def get_webdriver():
    """Get the WebDriver instance, initializing or restarting if necessary."""
    global driver, driver_last_used

    with driver_lock:
        # Check if driver needs to be initialized
        if driver is None:
            driver = initialize_webdriver()
            if driver is None:
                raise Exception("Failed to initialize WebDriver after multiple attempts")
            return driver

        # Check if driver has been idle too long and needs restart
        current_time = time.time()
        if current_time - driver_last_used > driver_max_idle_time:
            print("WebDriver has been idle too long. Restarting...")
            try:
                driver.quit()
            except:
                pass  # Ignore errors during quit
            driver = initialize_webdriver()
            if driver is None:
                raise Exception("Failed to reinitialize WebDriver after idle timeout")

        # Update last used time
        driver_last_used = current_time
        return driver

def cleanup_webdriver():
    """Clean up WebDriver resources."""
    global driver
    if driver:
        try:
            driver.quit()
        except:
            pass
        driver = None

# Register cleanup function to run on exit
atexit.register(cleanup_webdriver)

def SetAssistantStatus(Status):
    with open(os.path.join(TempDirPath, "Status.data"), "w", encoding="utf-8") as file:
        file.write(Status)

def QueryModifier(Query):
    """Format the query with proper punctuation and capitalization."""
    if not Query or Query.strip() == "":
        return "I didn't catch that."

    new_query = Query.lower().strip()
    query_words = new_query.split()

    if not query_words:
        return "I didn't catch that."

    question_words = ["how", "what", "who", "where", "when", "why", "which", "whose", "whom", "can you", "where's", "what's", "how's"]

    if any(word + " " in new_query for word in question_words):
        if len(query_words) > 0 and query_words[-1][-1] in ['.', '?', '!']:
            new_query = new_query[:-1] + "?"
        else:
            new_query += "?"
    else:
        if len(query_words) > 0 and query_words[-1][-1] in ['.', '?', '!']:
            new_query = new_query[:-1] + "."
        else:
            new_query += "."

    return new_query.capitalize()

def UniversalTranslator(Text):
    """Translate text to English from any detected language."""
    try:
        english_translation = mt.translate(Text, "en", "auto")
        return english_translation.capitalize()
    except Exception as e:
        print(f"Translation error: {e}")
        return Text.capitalize()  # Return original text if translation fails

def wait_for_speech_recognition(driver, timeout=30):
    """Wait for speech recognition to complete with timeout."""
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            # Check for recognized text
            text = driver.find_element(By.ID, "output").text
            if text and text.strip():
                return text

            # Check for errors
            error_text = driver.find_element(By.ID, "error").text
            if error_text and "no-speech" not in error_text:
                print(f"Speech recognition error: {error_text}")

            # Check status
            status_text = driver.find_element(By.ID, "status").text
            if "Recognition ended" in status_text and not text:
                # Recognition ended without text, might need to restart
                return None

            time.sleep(0.2)  # Short sleep to prevent CPU overuse
        except Exception as e:
            print(f"Error while waiting for speech: {e}")
            time.sleep(0.5)

    print("Speech recognition timed out")
    return None  # Timeout reached

def SpeechRecognition():
    """
    Main speech recognition function with improved error handling and recovery.

    This function captures speech input and returns the recognized text.
    The system supports various capabilities including:
    - General conversation
    - Real-time information retrieval
    - Opening/closing applications
    - Playing music
    - Image generation (using "generate image [prompt]" command)
    - System controls
    - Content creation
    - Web searches

    As a voice assistant, the system can multitask efficiently:
    - Generate images while continuing to respond to other commands
    - Open multiple applications simultaneously
    - Handle various tasks in parallel without interruption
    - Provide verbal feedback about ongoing processes (e.g., "I'm generating your image now")
    """
    recognition_attempts = 0
    max_attempts = 3
    timeout_seconds = 30

    while recognition_attempts < max_attempts:
        try:
            print("🎤 Attempting speech recognition...")
            SetAssistantStatus("Listening...")

            # Try to get a WebDriver instance (new or existing)
            try:
                local_driver = get_webdriver()
                if not local_driver:
                    raise Exception("Failed to get WebDriver")
                print("✅ WebDriver initialized successfully")
            except Exception as e:
                print(f"❌ WebDriver initialization failed: {e}")
                recognition_attempts += 1
                if recognition_attempts >= max_attempts:
                    return "I'm having trouble with speech recognition. Please try again later."
                time.sleep(2)
                continue

            # Load the speech recognition page
            try:
                print(f"📄 Loading HTML page: {Link}")
                local_driver.get("file:///" + Link)
                print("✅ HTML page loaded successfully")
            except Exception as e:
                print(f"❌ Failed to load HTML page: {e}")
                recognition_attempts += 1
                continue

            # Start speech recognition
            try:
                print("🔘 Clicking start button...")
                local_driver.find_element(By.ID, "start").click()
                print("✅ Speech recognition started")
                SetAssistantStatus("Listening...")
            except Exception as e:
                print(f"❌ Failed to start speech recognition: {e}")
                recognition_attempts += 1
                continue

            # Wait for speech to be recognized
            recognized_text = wait_for_speech_recognition(local_driver, timeout_seconds)

            # If we got text, process it
            if recognized_text:
                print(f"🗣️ Recognized text: {recognized_text}")
                try:
                    # Stop recognition
                    local_driver.find_element(By.ID, "end").click()
                    print("⏹️ Speech recognition stopped")
                except Exception as e:
                    print(f"⚠️ Error stopping recognition: {e}")
                    # Continue anyway since we have the text

                # Process the recognized text
                if "en" in InputLanguage.lower():
                    return QueryModifier(recognized_text)
                else:
                    SetAssistantStatus("Translating.....")
                    return QueryModifier(UniversalTranslator(recognized_text))

            # No text recognized, try again
            recognition_attempts += 1
            print(f"🔄 No speech recognized. Attempt {recognition_attempts}/{max_attempts}")

            # Try to stop current recognition before retrying
            try:
                local_driver.find_element(By.ID, "end").click()
            except:
                pass

            if recognition_attempts >= max_attempts:
                return "I didn't hear anything. Please try again."

            # Short delay before next attempt
            time.sleep(1)

        except WebDriverException as e:
            print(f"❌ WebDriver error in SpeechRecognition: {e}")
            # Force driver restart on WebDriver errors
            with driver_lock:
                global driver
                try:
                    if driver:
                        driver.quit()
                except:
                    pass
                driver = None
            recognition_attempts += 1

        except Exception as e:
            print(f"❌ Critical error in SpeechRecognition: {e}")
            recognition_attempts += 1
            time.sleep(1)

    return "I'm having trouble with speech recognition. Please try again later."

# Health check thread to periodically verify and restart the WebDriver if needed
def webdriver_health_check():
    """Periodically check WebDriver health and restart if necessary."""
    health_check_interval = 300  # 5 minutes

    while True:
        time.sleep(health_check_interval)

        with driver_lock:
            global driver
            if driver is None:
                continue  # No driver to check

            try:
                # Simple health check - try to access a property
                _ = driver.current_url
                print("WebDriver health check: OK")
            except Exception as e:
                print(f"WebDriver health check failed: {e}")
                try:
                    driver.quit()
                except:
                    pass
                driver = None
                print("WebDriver will be reinitialized on next use")

# Start health check thread when module is imported
health_check_thread = threading.Thread(target=webdriver_health_check, daemon=True)
health_check_thread.start()

if __name__ == "__main__":
    while True:
        Text = SpeechRecognition()
        print(Text)
